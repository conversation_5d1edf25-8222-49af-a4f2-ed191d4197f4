-- Create the B2F (Back to Front) pick view
-- This view shows discs that are candidates for moving from back stock to front stock

CREATE OR REPLACE VIEW public.v_b2f_pick_slim AS
SELECT
  d.g_pull as disc,
  osl.g_code as osl,
  vs.discs_sold_last_30_days_retail + vs.discs_sold_last_30_days_dz as osl_sold_last_30_dz_plus_retail
FROM
  t_discs d
  JOIN t_order_sheet_lines osl ON d.order_sheet_line_id = osl.id
  JOIN v_stats_by_osl vs ON osl.id = vs.id
WHERE
  vs.discs_in_stock_bs > 0
  AND (vs.discs_in_stock_fs + vs.discs_in_stock_b2f) = 0
  AND d.location = 'BS'::text
  AND d.sold_date IS NULL
ORDER BY
  d.order_sheet_line_id,
  d.g_pull;

-- Grant permissions
GRANT SELECT ON public.v_b2f_pick_slim TO anon;
GRANT SELECT ON public.v_b2f_pick_slim TO authenticated;
