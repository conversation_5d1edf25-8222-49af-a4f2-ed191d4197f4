/**
 * Informed Direct Importer
 *
 * This module imports Informed Repricer CSV files directly into Supabase
 * using the Supabase JavaScript client, without relying on external tools.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { parse } from 'csv-parse';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get the directory name of the current module
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Constants
const REPORTS_DIR = path.join(__dirname, 'data', 'external data');
const BATCH_SIZE = 100; // Number of records to insert in a single batch

// Supabase client
const supabaseUrl = process.env.SUPABASE_URL || 'https://aepabhlwpjfjulrjeitn.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMzc4NjU0MSwiZXhwIjoyMDQ5MzYyNTQxfQ.FQyPTgdBP83VhuykdlZkagHADc5nBmx7-yd0JYt5aP8';



const supabase = createClient(supabaseUrl, supabaseKey);

// Report configurations
const REPORT_CONFIGS = [
    {
        type: 'All_Fields',
        filename: 'from_informed_all_fields.csv',
        tableName: 'it_infor_all_fields'
    },
    {
        type: 'Competition_Landscape',
        filename: 'from_informed_competition_landscape.csv',
        tableName: 'it_infor_competition_landscape'
    },
    {
        type: 'No_Buy_Box',
        filename: 'from_informed_no_buy_box.csv',
        tableName: 'it_infor_no_buy_box'
    }
];

// Column mapping for different table structures
const COLUMN_MAPPINGS = {
    'it_infor_all_fields': {
        'SALES_RANK': 'Sales_Rank'
        // Add more mappings as needed
    },
    'it_infor_competition_landscape': {
        // Add mappings if needed
    },
    'it_infor_no_buy_box': {
        // Add mappings if needed
    }
};

/**
 * Parse a CSV file and return an array of records
 * @param {string} filePath - Path to the CSV file
 * @param {string} tableName - Name of the target table for column mapping
 * @returns {Promise<Array>} - Array of records
 */
async function parseCSV(filePath, tableName) {
    return new Promise((resolve, reject) => {
        const records = [];

        fs.createReadStream(filePath)
            .pipe(parse({
                columns: true,
                skip_empty_lines: true,
                trim: true
            }))
            .on('data', (record) => {
                // Apply column mappings if they exist for this table
                const mappings = COLUMN_MAPPINGS[tableName] || {};
                const mappedRecord = {};

                Object.keys(record).forEach(key => {
                    // Use mapped column name if it exists, otherwise use original
                    const mappedKey = mappings[key] || key;

                    // Convert empty strings to null for numeric fields
                    let value = record[key];
                    if (value === '') {
                        value = null;
                    }

                    mappedRecord[mappedKey] = value;
                });

                records.push(mappedRecord);
            })
            .on('end', () => {
                console.log(`Parsed ${records.length} records from ${filePath}`);
                if (records.length > 0) {
                    console.log(`Sample record columns:`, Object.keys(records[0]));
                }
                resolve(records);
            })
            .on('error', (error) => {
                reject(error);
            });
    });
}

/**
 * Truncate a table by deleting records in batches
 * @param {string} tableName - Name of the table to truncate
 * @returns {Promise<Object>} - Result of the operation
 */
async function truncateTable(tableName) {
    console.log(`Truncating table ${tableName}`);

    try {
        // First, get the count of records
        const { count, error: countError } = await supabase
            .from(tableName)
            .select('*', { count: 'exact', head: true });

        if (countError) {
            console.error(`Error getting count for table ${tableName}:`, countError);
            return {
                success: false,
                tableName,
                error: countError.message
            };
        }

        console.log(`Table ${tableName} has ${count} records to delete`);

        if (count === 0) {
            console.log(`Table ${tableName} is already empty`);
            return {
                success: true,
                tableName
            };
        }

        // Delete records in batches
        const batchSize = 1000;
        let deletedCount = 0;

        while (deletedCount < count) {
            // Get a batch of records to delete (only select SKU to minimize data transfer)
            const { data: recordsToDelete, error: selectError } = await supabase
                .from(tableName)
                .select('SKU')
                .limit(batchSize);

            if (selectError) {
                console.error(`Error selecting records from table ${tableName}:`, selectError);
                return {
                    success: false,
                    tableName,
                    error: selectError.message
                };
            }

            if (!recordsToDelete || recordsToDelete.length === 0) {
                console.log(`No more records to delete from table ${tableName}`);
                break;
            }

            console.log(`Deleting batch of ${recordsToDelete.length} records from ${tableName}`);

            // Delete the batch using SKU filter
            const skusToDelete = recordsToDelete.map(record => record.SKU);
            const { error: deleteError } = await supabase
                .from(tableName)
                .delete()
                .in('SKU', skusToDelete);

            if (deleteError) {
                console.error(`Error deleting batch from table ${tableName}:`, deleteError);
                return {
                    success: false,
                    tableName,
                    error: deleteError.message
                };
            }

            deletedCount += recordsToDelete.length;
            console.log(`Deleted ${deletedCount}/${count} records from ${tableName}`);

            // Small delay to avoid overwhelming the database
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        // Verify the table is empty
        const { count: finalCount, error: finalCountError } = await supabase
            .from(tableName)
            .select('*', { count: 'exact', head: true });

        if (finalCountError) {
            console.warn(`Warning: Could not verify final count for table ${tableName}:`, finalCountError);
        } else {
            console.log(`Table ${tableName} final count: ${finalCount}`);
        }

        return {
            success: true,
            tableName,
            deletedCount
        };
    } catch (error) {
        console.error(`Error truncating table ${tableName}:`, error);
        return {
            success: false,
            tableName,
            error: error.message
        };
    }
}

/**
 * Insert records into a table in batches
 * @param {string} tableName - Name of the table to insert into
 * @param {Array} records - Array of records to insert
 * @returns {Promise<Object>} - Result of the operation
 */
async function insertRecords(tableName, records) {
    console.log(`Inserting ${records.length} records into ${tableName}`);

    try {
        // Insert records in batches
        const batches = [];
        for (let i = 0; i < records.length; i += BATCH_SIZE) {
            const batch = records.slice(i, i + BATCH_SIZE);
            batches.push(batch);
        }

        let successCount = 0;
        let errorCount = 0;

        for (let i = 0; i < batches.length; i++) {
            const batch = batches[i];
            console.log(`Inserting batch ${i + 1}/${batches.length} (${batch.length} records) into ${tableName}`);

            const { data, error } = await supabase.from(tableName).insert(batch);

            if (error) {
                console.error(`Error inserting batch ${i + 1} into ${tableName}:`, error);
                errorCount += batch.length;
            } else {
                successCount += batch.length;
            }
        }

        return {
            success: errorCount === 0,
            tableName,
            recordCount: records.length,
            successCount,
            errorCount
        };
    } catch (error) {
        console.error(`Error inserting records into ${tableName}:`, error);
        return {
            success: false,
            tableName,
            error: error.message
        };
    }
}



/**
 * Import a single report into its corresponding table
 * @param {Object} report - Report configuration object
 * @returns {Promise<Object>} - Result of the import operation
 */
async function importReport(report) {
    const filePath = path.join(REPORTS_DIR, report.filename);

    console.log(`Importing ${report.type} report from ${filePath} to ${report.tableName}`);

    try {
        // Check if file exists
        if (!fs.existsSync(filePath)) {
            return {
                success: false,
                report: report.type,
                error: `File not found: ${filePath}`
            };
        }

        // Parse CSV with column mapping
        const records = await parseCSV(filePath, report.tableName);

        if (records.length === 0) {
            return {
                success: false,
                report: report.type,
                error: 'No records found in CSV file'
            };
        }

        // Truncate table
        const truncateResult = await truncateTable(report.tableName);

        if (!truncateResult.success) {
            return {
                success: false,
                report: report.type,
                error: `Error truncating table: ${truncateResult.error}`
            };
        }

        // Insert records
        const insertResult = await insertRecords(report.tableName, records);

        if (!insertResult.success) {
            return {
                success: false,
                report: report.type,
                error: `Error inserting records: ${insertResult.error}`
            };
        }

        return {
            success: insertResult.success,
            report: report.type,
            recordCount: insertResult.recordCount,
            successCount: insertResult.successCount,
            errorCount: insertResult.errorCount
        };
    } catch (error) {
        console.error(`Error importing ${report.type} report:`, error);
        return {
            success: false,
            report: report.type,
            error: error.message
        };
    }
}

/**
 * Import all reports
 * @returns {Promise<Array>} - Array of import results
 */
export async function importAllReports() {
    console.log('Importing all Informed reports');

    const results = [];

    for (const report of REPORT_CONFIGS) {
        const result = await importReport(report);
        results.push(result);
    }

    return results;
}

/**
 * Import a specific report by type
 * @param {string} reportType - Type of report to import
 * @returns {Promise<Object>} - Result of the import operation
 */
export async function importReportByType(reportType) {
    console.log(`Importing Informed report: ${reportType}`);

    const report = REPORT_CONFIGS.find(r => r.type === reportType);

    if (!report) {
        return {
            success: false,
            error: `Unknown report type: ${reportType}`
        };
    }

    return await importReport(report);
}

/**
 * Main function for running the script directly
 */
async function main() {
    try {
        console.log('Starting Informed Repricer data import...');

        const results = await importAllReports();

        console.log('Import results:', results);

        const allSuccessful = results.every(result => result.success);

        if (allSuccessful) {
            console.log('All imports completed successfully.');
        } else {
            console.error('Some imports failed.');
        }
    } catch (error) {
        console.error('Error running import:', error);
    }
}

// Run the main function if this script is executed directly
if (process.argv[1] === fileURLToPath(import.meta.url)) {
    main();
}
