// Test Auto-Select B2F API endpoint
import fetch from 'node-fetch';

async function testAutoSelectB2F() {
    console.log('🧪 Testing Auto-Select B2F API...\n');
    
    try {
        console.log('🎯 Testing /api/b2f/auto-select with maxOsls=2...');
        
        const response = await fetch('http://localhost:3001/api/b2f/auto-select', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                maxOsls: 2  // Test with just 2 OSLs
            }),
        });
        
        const data = await response.json();
        
        if (data.success) {
            console.log(`✅ Auto-selection successful!`);
            console.log(`📊 Summary:`);
            console.log(`   - OSLs processed: ${data.summary.totalOsls}`);
            console.log(`   - Discs selected: ${data.summary.totalDiscs}`);
            console.log(`   - Successful updates: ${data.summary.successfulUpdates}`);
            console.log(`   - Errors: ${data.summary.errors}`);
            
            if (data.selectedDiscs && data.selectedDiscs.length > 0) {
                console.log(`\n📋 Selected Discs by OSL:`);
                data.selectedDiscs.forEach(osl => {
                    console.log(`\n${osl.osl} (needed: ${osl.needed}, selected: ${osl.selected})`);
                    osl.discs.forEach(disc => {
                        console.log(`  • ${disc.disc} (Grade: ${disc.grade})`);
                    });
                });
            }
            
            if (data.errors && data.errors.length > 0) {
                console.log(`\n⚠️ Errors:`);
                data.errors.forEach(error => {
                    console.log(`  • ${error}`);
                });
            }
        } else {
            console.log(`❌ Auto-selection failed: ${data.error}`);
        }
        
    } catch (error) {
        console.log(`❌ API test failed: ${error.message}`);
    }
    
    console.log('\n🎉 Auto-Select B2F API Test Complete!');
}

testAutoSelectB2F();
